'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

export default function PaymentSuccessToast() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');
    const amount = searchParams.get('amount');
    const orderNo = searchParams.get('order_no');

    if (payment === 'success') {
      // 显示支付成功提示
      let message = '🎉 支付成功！';
      
      if (credits) {
        message += ` 已为您添加 ${credits} 积分`;
      }
      
      if (amount) {
        message += `，支付金额 $${amount}`;
      }

      if (orderNo) {
        message += `，订单号：${orderNo}`;
      }

      toast.success(message, {
        duration: 5000,
        description: '您可以立即开始使用新增的积分来去除图片水印。',
      });

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('credits');
      url.searchParams.delete('amount');
      url.searchParams.delete('order_no');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  return null;
}
