import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import {
  findOrderByOrderNo,
  OrderStatus,
  updateOrderStatus,
} from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { db } from "@/db";
import { orders } from "@/db/schema";
import { eq, desc } from "drizzle-orm";

import Strip<PERSON> from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { Order } from "@/types/order";
import { PaymentCallbackSession } from "@/types/payment";

export interface CreateOrderParams {
    user_uuid: string;
    user_email: string;
    plan_id: string;
    credits: number;
    price: number;
    currency: string;
    interval: string;
    valid_months: number;
    status: 'pending' | 'completed' | 'failed' | 'cancelled';
}

/**
 * 处理Stripe支付会话（保持向后兼容）
 */
export async function handleOrderSession(session: Stripe.Checkout.Session) {
  const paymentSession: PaymentCallbackSession = {
    id: session.id,
    payment_status: session.payment_status || 'unpaid',
    customer_email: session.customer_email || undefined,
    customer_details: session.customer_details ? {
      email: session.customer_details.email || undefined
    } : undefined,
    metadata: session.metadata || undefined,
    amount_total: session.amount_total || undefined,
    currency: session.currency || undefined,
  };

  return handlePaymentSession(paymentSession);
}

/**
 * 处理通用支付会话
 * 遵循DRY原则：统一处理不同支付方式的回调
 */
export async function handlePaymentSession(session: PaymentCallbackSession) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== OrderStatus.Created) {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(
      order_no,
      OrderStatus.Paid,
      paid_at,
      paid_email,
      paid_detail
    );

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paid order
        await updateCreditForOrder(order as unknown as Order);
      }

      // update affiliate for paid order
      await updateAffiliateForOrder(order as unknown as Order);
    }

    console.log(
      "handle payment session succeeded: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle payment session failed: ", e);
    throw e;
  }
}

export async function createOrder(params: CreateOrderParams) {
  try {
    const orderId = getUuid();
    const now = getIsoTimestr();

    const [order] = await db().insert(orders).values({
      order_no: orderId,
      user_email: params.user_email,
      product_id: params.plan_id,
      credits: params.credits,
      amount: params.price,
      currency: params.currency,
      interval: params.interval,
      valid_months: params.valid_months,
      status: params.status,
      created_at: new Date(now),
      user_uuid: params.user_uuid,
    }).returning();

    return order;
  } catch (error) {
    console.error("Create order error:", error);
    throw new Error("Failed to create order");
  }
}

export async function updateOrderStatusById(orderId: string, status: string) {
  try {
    const now = getIsoTimestr();

    const [order] = await db().update(orders)
      .set({
        status,
      })
      .where(eq(orders.order_no, orderId))
      .returning();

    return order;
  } catch (error) {
    console.error("Update order status error:", error);
    throw new Error("Failed to update order status");
  }
}

export async function getOrdersByUser(userEmail: string) {
  try {
    const userOrders = await db().select()
      .from(orders)
      .where(eq(orders.user_email, userEmail))
      .orderBy(desc(orders.created_at));

    return userOrders;
  } catch (error) {
    console.error("Get orders by user error:", error);
    throw new Error("Failed to get user orders");
  }
}

export async function getAllOrders() {
  try {
    const allOrders = await db().select()
      .from(orders)
      .orderBy(desc(orders.created_at));

    return allOrders;
  } catch (error) {
    console.error("Get all orders error:", error);
    throw new Error("Failed to get all orders");
  }
}
