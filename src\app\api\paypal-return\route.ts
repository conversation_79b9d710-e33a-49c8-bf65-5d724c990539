import { redirect } from "next/navigation";
import { getPaymentService } from "@/services/payment";
import { handlePaymentSession } from "@/services/order";

/**
 * PayPal支付返回处理
 * PayPal会重定向到这个端点，并在URL中包含token参数（即PayPal订单ID）
 */
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const token = url.searchParams.get('token'); // PayPal订单ID
    const orderNo = url.searchParams.get('order_no'); // 我们的订单号

    if (!token) {
      console.error('PayPal return missing token parameter');
      redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
      return;
    }

    // 使用PayPal订单ID获取支付会话信息并处理
    const paymentService = getPaymentService();
    const session = await paymentService.getPaymentSession(token);
    
    await handlePaymentSession(session);

    // 支付成功，重定向到成功页面，并传递支付信息
    try {
      const orderNo = session.metadata?.order_no;
      const credits = session.metadata?.credits;
      const amount = session.amount_total ? (session.amount_total / 100).toFixed(2) : '';

      const successParams = new URLSearchParams({
        payment: 'success',
      });

      if (orderNo) successParams.set('order_no', orderNo);
      if (credits) successParams.set('credits', credits);
      if (amount) successParams.set('amount', amount);

      const successUrl = `${process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/my-orders"}?${successParams.toString()}`;
      redirect(successUrl);
    } catch (e) {
      console.error('Error building PayPal success URL:', e);
      redirect(process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/my-orders?payment=success");
    }
  } catch (e) {
    console.error("PayPal return processing failed:", e);
    redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
  }
}
